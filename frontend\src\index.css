
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Enhanced Light Theme Color Palette */
    --background: 0 0% 100%;
    --foreground: 215 25% 27%;

    --card: 0 0% 100%;
    --card-foreground: 215 25% 27%;

    --popover: 0 0% 100%;
    --popover-foreground: 215 25% 27%;

    /* Primary: Modern Blue */
    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 100%;

    /* Secondary: Soft Gray */
    --secondary: 210 40% 96%;
    --secondary-foreground: 215 25% 27%;

    /* Muted: Light Gray */
    --muted: 210 40% 96%;
    --muted-foreground: 215 16% 47%;

    /* Accent: Energetic Green */
    --accent: 142 76% 36%;
    --accent-foreground: 0 0% 100%;

    /* Success: Green */
    --success: 142 76% 36%;
    --success-foreground: 0 0% 100%;

    /* Warning: Orange */
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 100%;

    /* Destructive: Red */
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    /* Borders and Inputs */
    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 217 91% 60%;

    /* Radius */
    --radius: 0.75rem;

    /* Sidebar */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 215 25% 27%;
    --sidebar-primary: 217 91% 60%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 210 40% 96%;
    --sidebar-accent-foreground: 215 25% 27%;
    --sidebar-border: 214 32% 91%;
    --sidebar-ring: 217 91% 60%;

    /* Custom Variables for Enhanced UI */
    --gradient-primary: linear-gradient(135deg, hsl(217 91% 60%) 0%, hsl(142 76% 36%) 100%);
    --gradient-secondary: linear-gradient(135deg, hsl(210 40% 96%) 0%, hsl(0 0% 100%) 100%);
    --shadow-soft: 0 2px 8px -2px rgba(0, 0, 0, 0.05);
    --shadow-medium: 0 4px 16px -4px rgba(0, 0, 0, 0.1);
    --shadow-large: 0 8px 32px -8px rgba(0, 0, 0, 0.15);
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
  }

  /* Enhanced Typography */
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold tracking-tight;
  }

  /* Smooth Transitions */
  * {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

@layer components {
  /* Enhanced Card Styles */
  .card-enhanced {
    @apply bg-white/80 backdrop-blur-sm border border-gray-200/50 rounded-xl shadow-sm hover:shadow-md transition-all duration-300;
  }

  .card-gradient {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* Enhanced Button Styles */
  .btn-gradient {
    background: var(--gradient-primary);
    @apply text-white border-0 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 transition-all duration-200;
  }

  /* Glassmorphism Effect */
  .glass {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
  }

  /* Enhanced Shadows */
  .shadow-soft {
    box-shadow: var(--shadow-soft);
  }

  .shadow-medium {
    box-shadow: var(--shadow-medium);
  }

  .shadow-large {
    box-shadow: var(--shadow-large);
  }
}

@layer utilities {
  /* 3D Card Effects */
  .perspective-1000 {
    perspective: 1000px;
  }

  .transform-style-preserve-3d {
    transform-style: preserve-3d;
  }

  .backface-hidden {
    backface-visibility: hidden;
  }

  .rotate-y-180 {
    transform: rotateY(180deg);
  }

  /* Enhanced Gradients */
  .bg-gradient-primary {
    background: var(--gradient-primary);
  }

  .bg-gradient-secondary {
    background: var(--gradient-secondary);
  }

  .bg-gradient-soft {
    background: linear-gradient(135deg, #f1f5f9 0%, #ffffff 100%);
  }

  /* Enhanced Text Gradients */
  .text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Animation Classes */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.5s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.3s ease-out;
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
}
