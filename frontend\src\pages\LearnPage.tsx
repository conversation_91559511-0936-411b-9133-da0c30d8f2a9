
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { BookOpen, Brain, Target, BarChart3, Clock, Trophy, Zap } from "lucide-react";
import { Header } from "@/components/layout/Header";
import EnhancedFlashcardLearning from "@/components/EnhancedFlashcardLearning";
import QuizSystem from "@/components/QuizSystem";
import { useLibrary } from "@/contexts/LibraryContext";
import { useLearning } from "@/contexts/LearningContext";

const LearnPage = () => {
  const { libraries, selectedLibrary } = useLibrary();
  const { learningStats, isSessionActive } = useLearning();
  const [activeTab, setActiveTab] = useState("dashboard");

  // If there's an active session, show the appropriate learning component
  if (isSessionActive) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
        <Header />
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsContent value="flashcards">
            <EnhancedFlashcardLearning />
          </TabsContent>
          <TabsContent value="quiz">
            <QuizSystem />
          </TabsContent>
        </Tabs>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      <Header />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* Page Header */}
          <div className="text-center space-y-4">
            <h1 className="text-4xl font-bold text-gray-900">Learning Center</h1>
            <p className="text-xl text-gray-600">Master your vocabulary with interactive learning tools</p>
          </div>

          {/* Learning Modes */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full max-w-md mx-auto grid-cols-3">
              <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
              <TabsTrigger value="flashcards">Flashcards</TabsTrigger>
              <TabsTrigger value="quiz">Quiz</TabsTrigger>
            </TabsList>

            {/* Dashboard Tab */}
            <TabsContent value="dashboard" className="space-y-8">
              {/* Quick Stats */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-blue-100 rounded-lg">
                        <BookOpen className="h-6 w-6 text-blue-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-600">Words Studied</p>
                        <p className="text-2xl font-bold text-gray-900">{learningStats.totalWordsStudied}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-green-100 rounded-lg">
                        <Target className="h-6 w-6 text-green-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-600">Accuracy</p>
                        <p className="text-2xl font-bold text-gray-900">{learningStats.averageAccuracy.toFixed(1)}%</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-purple-100 rounded-lg">
                        <Clock className="h-6 w-6 text-purple-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-600">Time Spent</p>
                        <p className="text-2xl font-bold text-gray-900">{Math.round(learningStats.totalTimeSpent)}m</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-orange-100 rounded-lg">
                        <Trophy className="h-6 w-6 text-orange-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-600">Streak</p>
                        <p className="text-2xl font-bold text-gray-900">{learningStats.streakDays} days</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Learning Options */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {/* Flashcards */}
                <Card className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-3">
                      <div className="p-2 bg-blue-100 rounded-lg">
                        <Brain className="h-6 w-6 text-blue-600" />
                      </div>
                      <div>
                        <h3 className="text-xl font-bold">Flashcard Learning</h3>
                        <p className="text-sm text-gray-600">Interactive card-based learning</p>
                      </div>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Available Libraries:</span>
                        <span>{libraries.length}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Total Words:</span>
                        <span>{libraries.reduce((sum, lib) => sum + lib.word_count, 0)}</span>
                      </div>
                    </div>
                    <Button
                      onClick={() => setActiveTab("flashcards")}
                      className="w-full"
                      size="lg"
                    >
                      <Zap className="h-4 w-4 mr-2" />
                      Start Flashcards
                    </Button>
                  </CardContent>
                </Card>

                {/* Quiz System */}
                <Card className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-3">
                      <div className="p-2 bg-green-100 rounded-lg">
                        <Target className="h-6 w-6 text-green-600" />
                      </div>
                      <div>
                        <h3 className="text-xl font-bold">Vocabulary Quiz</h3>
                        <p className="text-sm text-gray-600">Test your knowledge</p>
                      </div>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Question Types:</span>
                        <span>3 Types</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Best Accuracy:</span>
                        <span>{learningStats.averageAccuracy.toFixed(1)}%</span>
                      </div>
                    </div>
                    <Button
                      onClick={() => setActiveTab("quiz")}
                      className="w-full"
                      size="lg"
                      variant="outline"
                    >
                      <Brain className="h-4 w-4 mr-2" />
                      Take Quiz
                    </Button>
                  </CardContent>
                </Card>
              </div>

              {/* Library Overview */}
              {selectedLibrary && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <BookOpen className="h-5 w-5" />
                      <span>Current Library: {selectedLibrary.name}</span>
                      {selectedLibrary.is_master && <Badge variant="secondary">Master</Badge>}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="text-center">
                        <p className="text-3xl font-bold text-blue-600">{selectedLibrary.word_count}</p>
                        <p className="text-sm text-gray-600">Total Words</p>
                      </div>
                      <div className="text-center">
                        <p className="text-3xl font-bold text-green-600">{selectedLibrary.learned_count}</p>
                        <p className="text-sm text-gray-600">Learned</p>
                      </div>
                      <div className="text-center">
                        <p className="text-3xl font-bold text-orange-600">{selectedLibrary.unlearned_count}</p>
                        <p className="text-sm text-gray-600">To Learn</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            {/* Flashcards Tab */}
            <TabsContent value="flashcards">
              <EnhancedFlashcardLearning />
            </TabsContent>

            {/* Quiz Tab */}
            <TabsContent value="quiz">
              <QuizSystem />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default LearnPage;
